from django.contrib import admin
from .models import *
from datetime import datetime
from django.utils.translation import gettext_lazy as _
from .models import Conference

# Register your models her
class ParticipantPresenceFilter(admin.SimpleListFilter):
    title = _('Présence des participants')
    parameter_name = 'has_participants'

    def lookups(self, request, model_admin):
        return [
            ('yes', _('Avec participants')),
            ('no', _('Sans participants')),
        ]

    def queryset(self, request, queryset):
        value = self.value()
        if value == 'yes':
            return queryset.filter(reservations__isnull=False).distinct()
        elif value == 'no':
            return queryset.filter(reservations__isnull=True).distinct()
        return queryset  # Important pour afficher tous les résultats si aucun filtre n'est sélectionné

class ConferenceDateFFilter(admin.SimpleListFilter):
    title = 'Conference Date '
    parameter_name = 'Conference Date'

    def lookups(self, request, model_admin):
        return (
            ('past', 'Past Conference'),
            ('upcoming', 'Upcoming Conference'),
            ('today', 'Today Conference'),
            ('ongoing', 'Ongoing Conference')
        )
        
    def queryset(self, request, queryset):
        today = timezone.now().date()
        if self.value() == 'past':
            return queryset.filter(end_date__lt=today)
        if self.value() == 'upcoming':
            return queryset.filter(start_date__gte=today)
        if self.value() == 'today':
            return queryset.filter(start_date__lte=today) 
        if self.value() == 'ongoing':
            return queryset.filter(start_date__lte=today, end_date__gte=today)
        return queryset  # Return unfiltered queryset if no filter selected

  

class ConferenceAdmin(admin.ModelAdmin):
    list_display = ('title', 'location', 'start_date', 'category')
    search_fields = ('title', 'price',)
    list_per_page = 3
    readonly_fields = ('created_at', 'updated_at')
    ordering = ('start_date', 'title',)
    list_filter = ('category', ConferenceDateFFilter, ParticipantPresenceFilter,)
    autocomplete_fields = ('category',)
    fieldsets = (

       ('Description', {

           'fields': ('title', 'description'),

       }),

       ('Horaires de la conférence', {

           'fields': ('start_date', 'end_date'),

       }),

       ('Informations de la conférence', {

           'fields': ('location', 'price', 'capacity'),

       }),

       ('Documents', {

           'fields': ('program',),

       }),

       ('Catégorie', {

           'fields': ('category',),

       }),

       ('Timestamps', {

           'fields': ('created_at', 'updated_at'),


       }),

   )
class CategoryAdmin(admin.ModelAdmin):
    search_fields = ('title',)
 
admin.site.register(Conference, ConferenceAdmin)
admin.site.register(Category, CategoryAdmin)
admin.site.register(Reservation)
