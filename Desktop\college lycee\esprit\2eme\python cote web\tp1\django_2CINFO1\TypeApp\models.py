from django.db import models
from django.core.validators import MinLengthValidator, MaxLengthValidator

# Create your models here.

class Type(models.Model):
    name = models.CharField(
        max_length=100,
        validators=[
            MinLengthValidator(10, message="Le nom du type doit contenir au moins 10 caractères"),
            MaxLengthValidator(100, message="Le nom du type ne peut pas dépasser 100 caractères")
        ],
        verbose_name="Nom du type",
        help_text="Entre 10 et 100 caractères"
    )
    description = models.TextField(verbose_name="Description")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="Date de création")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="Date de modification")

    class Meta:
        verbose_name = "Type d'équipement"
        verbose_name_plural = "Types d'équipements"
        ordering = ['name']

    def __str__(self):
        return self.name
