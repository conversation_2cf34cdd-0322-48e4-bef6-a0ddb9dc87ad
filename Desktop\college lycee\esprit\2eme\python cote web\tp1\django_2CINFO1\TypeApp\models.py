from django.db import models

# Create your models here.

class Type(models.Model):
    name = models.CharField(max_length=100, verbose_name="Nom du type")
    description = models.TextField(verbose_name="Description")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="Date de création")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="Date de modification")

    class Meta:
        verbose_name = "Type d'équipement"
        verbose_name_plural = "Types d'équipements"
        ordering = ['name']

    def __str__(self):
        return self.name
