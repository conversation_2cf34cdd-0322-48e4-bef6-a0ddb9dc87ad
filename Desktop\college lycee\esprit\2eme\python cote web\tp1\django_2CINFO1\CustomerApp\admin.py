from django.contrib import admin
from .models import Customer

# Register your models here.

@admin.register(Customer)
class CustomerAdmin(admin.ModelAdmin):
    list_display = ['customer_id', 'first_name', 'last_name', 'email', 'username']
    list_filter = ['first_name', 'last_name']
    search_fields = ['first_name', 'last_name', 'email', 'username']
    readonly_fields = ['customer_id']

    fieldsets = (
        ('Informations personnelles', {
            'fields': ('first_name', 'last_name', 'email')
        }),
        ('Compte utilisateur', {
            'fields': ('username', 'password')
        }),
        ('Système', {
            'fields': ('customer_id',),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        # Hash the password if it's being set
        if 'password' in form.changed_data:
            from django.contrib.auth.hashers import make_password
            obj.password = make_password(obj.password)
        super().save_model(request, obj, form, change)
