# Generated by Django 5.2.1 on 2025-06-30 17:57

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("CustomerApp", "0001_initial"),
        ("EquipementApp", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Rental",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "rental_date",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="Date de location"
                    ),
                ),
                (
                    "return_date",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Date de retour prévue"
                    ),
                ),
                (
                    "actual_return_date",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Date de retour réelle"
                    ),
                ),
                (
                    "quantity",
                    models.PositiveIntegerField(
                        default=1, verbose_name="Quantité louée"
                    ),
                ),
                (
                    "daily_rate",
                    models.DecimalField(
                        decimal_places=2, max_digits=10, verbose_name="Tarif journalier"
                    ),
                ),
                (
                    "total_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=10,
                        null=True,
                        verbose_name="Montant total",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Actif"),
                            ("returned", "Retourné"),
                            ("overdue", "En retard"),
                            ("cancelled", "Annulé"),
                        ],
                        default="active",
                        max_length=20,
                        verbose_name="Statut",
                    ),
                ),
                ("notes", models.TextField(blank=True, verbose_name="Notes")),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="Date de création"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        auto_now=True, verbose_name="Date de modification"
                    ),
                ),
                (
                    "customer",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="rentals",
                        to="CustomerApp.customer",
                        verbose_name="Client",
                    ),
                ),
                (
                    "equipement",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="rentals",
                        to="EquipementApp.equipement",
                        verbose_name="Équipement",
                    ),
                ),
            ],
            options={
                "verbose_name": "Location",
                "verbose_name_plural": "Locations",
                "ordering": ["-rental_date"],
            },
        ),
    ]
