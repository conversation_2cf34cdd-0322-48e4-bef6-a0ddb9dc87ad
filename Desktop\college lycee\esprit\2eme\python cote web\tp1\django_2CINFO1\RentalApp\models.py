from django.db import models
from CustomerApp.models import Customer
from EquipementApp.models import Equipement
# Create your models here.

class Rental(models.Model):
    customer = models.ForeignKey(
        Customer, 
        on_delete=models.CASCADE, 
        related_name='rentals',
        verbose_name="Client"
    )
    equipement = models.ForeignKey(
        Equipement, 
        on_delete=models.CASCADE, 
        related_name='rentals',
        verbose_name="Équipement"
    )
    rental_date = models.DateTimeField(auto_now_add=True, verbose_name="Date de location")
    return_date = models.DateTimeField(null=True, blank=True, verbose_name="Date de retour prévue")
    returned = models.DateTimeField(null=True, blank=True, verbose_name="Date de retour prévue")
    class Meta:
        verbose_name = "Location"
        verbose_name_plural = "Locations"
        ordering = ['-rental_date']
    
    def __str__(self):
        return f"Location {self.id} - {self.customer.full_name} - {self.equipement.name}"
    
    @property
    def is_overdue(self):
        from django.utils import timezone
        if self.return_date and self.status == 'active':
            return timezone.now() > self.return_date
        return False
    
    def calculate_total_amount(self):
        if self.return_date and self.rental_date:
            days = (self.return_date - self.rental_date).days
            if days < 1:
                days = 1  # Minimum 1 day
            return self.daily_rate * days * self.quantity
        return None
    
    def save(self, *args, **kwargs):
        if not self.total_amount and self.return_date:
            self.total_amount = self.calculate_total_amount()
        super().save(*args, **kwargs)
