from django.db import models
from django.contrib.auth.models import AbstractUser
from django.core.validators import RegexValidator, MinLengthValidator
from django.core.exceptions import ValidationError
import re

# Create your models here.

def validate_customer_id(value):
    pattern = r'^[A-Za-z]{4}\d{4}$'
    if not re.match(pattern, value):
        raise ValidationError(
            'Le customer_id doit etre unique et respecter le format suivante: AAAA1111'
        )
 def validate_gmail(value):
    if not re.match(r'^[\w\.-]+@gmail\.com$', value):
        raise ValidationError("Veuillez entrer une adresse email valide appartenant au domaine gmail.com (ex : <EMAIL>).")


class Customer(models.Model):
    customer_id = models.CharField(
        max_length=8,
        primary_key=True,
        unique=True,
        validators=[validate_customer_id],
        verbose_name="ID Client",
    )
    first_name = models.CharField(max_length=50, verbose_name="Prénom")
    last_name = models.CharField(max_length=50, verbose_name="Nom de famille")
    email = models.EmailField(unique=True, verbose_name="Email", validators=[validate_gmail])
    username = models.CharField(max_length=50, unique=True, verbose_name="Nom d'utilisateur")
    password = models.CharField(max_length=128, verbose_name="Mot de passe")
    returned = models.BooleanField(default=False, verbose_name="Retourné")

    class Meta:
        verbose_name = "Client"
        verbose_name_plural = "Clients"
        ordering = ['last_name', 'first_name']

    def __str__(self):
        return f"{self.first_name} {self.last_name}"

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"
