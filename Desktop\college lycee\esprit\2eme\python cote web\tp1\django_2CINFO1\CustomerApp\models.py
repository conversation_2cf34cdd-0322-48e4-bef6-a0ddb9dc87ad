from django.db import models
from django.contrib.auth.models import AbstractUser

# Create your models here.

class Customer(models.Model):
    customer_id = models.AutoField(primary_key=True, verbose_name="ID Client")
    first_name = models.Char<PERSON>ield(max_length=50, verbose_name="Prénom")
    last_name = models.CharField(max_length=50, verbose_name="Nom de famille")
    email = models.EmailField(unique=True, verbose_name="Email")
    username = models.CharField(max_length=50, unique=True, verbose_name="Nom d'utilisateur")
    password = models.CharField(max_length=128, verbose_name="Mot de passe")

    class Meta:
        verbose_name = "Client"
        verbose_name_plural = "Clients"
        ordering = ['last_name', 'first_name']

    def __str__(self):
        return f"{self.first_name} {self.last_name}"

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"
