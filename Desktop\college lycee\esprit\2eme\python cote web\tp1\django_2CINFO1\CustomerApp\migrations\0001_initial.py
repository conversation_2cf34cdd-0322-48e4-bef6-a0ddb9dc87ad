# Generated by Django 5.2.1 on 2025-06-30 17:57

from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Customer",
            fields=[
                (
                    "customer_id",
                    models.AutoField(
                        primary_key=True, serialize=False, verbose_name="ID Client"
                    ),
                ),
                ("first_name", models.<PERSON>r<PERSON>ield(max_length=50, verbose_name="Prénom")),
                (
                    "last_name",
                    models.<PERSON>r<PERSON><PERSON>(max_length=50, verbose_name="Nom de famille"),
                ),
                (
                    "email",
                    models.EmailField(
                        max_length=254, unique=True, verbose_name="Email"
                    ),
                ),
                (
                    "username",
                    models.<PERSON><PERSON><PERSON><PERSON>(
                        max_length=50, unique=True, verbose_name="Nom d'utilisateur"
                    ),
                ),
                (
                    "password",
                    models.<PERSON>r<PERSON><PERSON>(max_length=128, verbose_name="Mot de passe"),
                ),
            ],
            options={
                "verbose_name": "Client",
                "verbose_name_plural": "Clients",
                "ordering": ["last_name", "first_name"],
            },
        ),
    ]
