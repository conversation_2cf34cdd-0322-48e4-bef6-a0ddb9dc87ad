Stack trace:
Frame         Function      Args
0007FFFF9440  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFF9440, 0007FFFF8340) msys-2.0.dll+0x1FE8E
0007FFFF9440  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9718) msys-2.0.dll+0x67F9
0007FFFF9440  000210046832 (000210286019, 0007FFFF92F8, 0007FFFF9440, 000000000000) msys-2.0.dll+0x6832
0007FFFF9440  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF9440  000210068E24 (0007FFFF9450, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF9720  00021006A225 (0007FFFF9450, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFD4B190000 ntdll.dll
7FFD28040000 aswhook.dll
7FFD4AEB0000 KERNEL32.DLL
7FFD48A40000 KERNELBASE.dll
7FFD493F0000 USER32.dll
7FFD490F0000 win32u.dll
7FFD4B090000 GDI32.dll
7FFD48DF0000 gdi32full.dll
7FFD489A0000 msvcp_win.dll
7FFD48870000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFD4A430000 advapi32.dll
7FFD499C0000 msvcrt.dll
7FFD4A550000 sechost.dll
7FFD4ABA0000 RPCRT4.dll
7FFD48970000 bcrypt.dll
7FFD480D0000 CRYPTBASE.DLL
7FFD49120000 bcryptPrimitives.dll
7FFD4B120000 IMM32.DLL
