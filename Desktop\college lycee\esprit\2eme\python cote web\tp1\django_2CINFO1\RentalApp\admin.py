from django.contrib import admin
from .models import Rental

# Register your models here.

@admin.register(Rental)
class RentalAdmin(admin.ModelAdmin):
    list_display = ['id', 'customer', 'equipement', 'rental_date', 'return_date', 'returned']
    list_filter = ['rental_date', 'return_date', 'equipement__type']
    search_fields = ['customer__first_name', 'customer__last_name', 'equipement__name']
    readonly_fields = ['rental_date']
    date_hierarchy = 'rental_date'

    fieldsets = (
        ('Location', {
            'fields': ('customer', 'equipement')
        }),
        ('Dates', {
            'fields': ('rental_date', 'return_date', 'returned')
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('customer', 'equipement', 'equipement__type')
