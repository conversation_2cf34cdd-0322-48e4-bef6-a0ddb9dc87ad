from django.contrib import admin
from .models import Rental

# Register your models here.

@admin.register(Rental)
class RentalAdmin(admin.ModelAdmin):
    list_display = ['id', 'customer', 'equipement', 'rental_date', 'return_date', 'status', 'total_amount']
    list_filter = ['status', 'rental_date', 'return_date', 'equipement__type']
    search_fields = ['customer__first_name', 'customer__last_name', 'equipement__name']
    readonly_fields = ['rental_date', 'created_at', 'updated_at', 'total_amount']
    date_hierarchy = 'rental_date'

    fieldsets = (
        ('Location', {
            'fields': ('customer', 'equipement', 'quantity')
        }),
        ('Dates et tarifs', {
            'fields': ('rental_date', 'return_date', 'actual_return_date', 'daily_rate', 'total_amount')
        }),
        ('Statut et notes', {
            'fields': ('status', 'notes')
        }),
        ('Système', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('customer', 'equipement', 'equipement__type')

    def save_model(self, request, obj, form, change):
        # Calculate total amount if return_date is set
        if obj.return_date and obj.rental_date and obj.daily_rate:
            obj.total_amount = obj.calculate_total_amount()
        super().save_model(request, obj, form, change)
