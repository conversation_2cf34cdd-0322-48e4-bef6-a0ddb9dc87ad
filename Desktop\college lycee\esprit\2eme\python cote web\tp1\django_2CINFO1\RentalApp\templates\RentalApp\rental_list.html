<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Liste des Locations</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
        .stats {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: center;
        }
        .stats strong {
            color: #007bff;
            font-size: 18px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #495057;
            position: sticky;
            top: 0;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .rental-id {
            font-weight: bold;
            color: #007bff;
        }
        .customer-info {
            color: #333;
        }
        .customer-id {
            font-weight: bold;
            color: #28a745;
        }
        .equipement-info {
            color: #333;
        }
        .rental-date {
            color: #6c757d;
            font-size: 14px;
        }
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-returned-true {
            background-color: #d4edda;
            color: #155724;
        }
        .status-returned-false {
            background-color: #fff3cd;
            color: #856404;
        }
        .no-results {
            text-align: center;
            padding: 40px;
            color: #6c757d;
            font-style: italic;
        }
        .table-responsive {
            overflow-x: auto;
        }
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 15px;
            }
            table {
                font-size: 14px;
            }
            th, td {
                padding: 8px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>
            🏠 Liste des Locations
        </h1>
        
        <!-- Statistiques -->
        <div class="stats">
            <strong>{{ rentals.count }}</strong> location(s) au total
        </div>

        <!-- Tableau des locations -->
        {% if rentals %}
            <div class="table-responsive">
                <table>
                    <thead>
                        <tr>
                            <th>ID Location</th>
                            <th>Customer</th>
                            <th>Équipement</th>
                            <th>Date de Location</th>
                            <th>Statut Retour</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for rental in rentals %}
                            <tr>
                                <td>
                                    <span class="rental-id">#{{ rental.id }}</span>
                                </td>
                                <td>
                                    <div class="customer-info">
                                        <div class="customer-id">{{ rental.customer.customer_id }}</div>
                                        <div>{{ rental.customer.first_name }} {{ rental.customer.last_name }}</div>
                                        <small style="color: #6c757d;">{{ rental.customer.email }}</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="equipement-info">
                                        <div><strong>{{ rental.equipement.name }}</strong></div>
                                        <div>{{ rental.equipement.brand }}</div>
                                        <small style="color: #6c757d;">Type: {{ rental.equipement.type.name }}</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="rental-date">
                                        {{ rental.rental_date|date:"d/m/Y H:i" }}
                                    </div>
                                </td>
                                <td>
                                    {% if rental.customer.returned %}
                                        <span class="status-badge status-returned-true">
                                            ✅ Retourné
                                        </span>
                                    {% else %}
                                        <span class="status-badge status-returned-false">
                                            ⏳ En cours
                                        </span>
                                    {% endif %}
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="no-results">
                Aucune location enregistrée pour le moment.
            </div>
        {% endif %}
    </div>
</body>
</html>
