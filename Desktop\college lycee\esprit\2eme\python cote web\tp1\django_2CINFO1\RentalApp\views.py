from django.shortcuts import render, get_object_or_404
from .models import Rental

# Create your views here.

def rental_list(request):
    """Vue pour afficher la liste des locations"""
    rentals = Rental.objects.select_related('customer', 'equipement').all().order_by('-rental_date')

    context = {
        'rentals': rentals,
    }

    return render(request, 'RentalApp/rental_list.html', context)

def rental_detail(request, rental_id):
    """Vue pour afficher les détails d'une location"""
    rental = get_object_or_404(Rental, id=rental_id)

    context = {
        'rental': rental,
    }

    return render(request, 'RentalApp/rental_detail.html', context)
