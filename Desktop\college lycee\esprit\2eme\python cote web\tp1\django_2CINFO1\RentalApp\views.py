from django.shortcuts import render, get_object_or_404, redirect
from django.contrib import messages
from .models import Rental

# Create your views here.

def rental_list(request):
    """Vue pour afficher la liste des locations"""
    rentals = Rental.objects.select_related('customer', 'equipement').all().order_by('-rental_date')

    context = {
        'rentals': rentals,
    }

    return render(request, 'RentalApp/rental_list.html', context)

def my_rentals(request):
    """Vue pour afficher les locations du customer connecté"""
    customer_id = request.session.get('customer_id')
    if not customer_id:
        messages.error(request, 'Vous devez vous connecter pour voir vos locations.')
        return redirect('CustomerApp:customer_login')

    try:
        rentals = Rental.objects.filter(customer__customer_id=customer_id).select_related('customer', 'equipement').order_by('-rental_date')

        context = {
            'rentals': rentals,
            'customer_id': customer_id,
        }

        return render(request, 'RentalApp/my_rentals.html', context)
    except Exception as e:
        messages.error(request, 'Erreur lors de la récupération de vos locations.')
        return redirect('CustomerApp:customer_login')

def rental_detail(request, rental_id):
    """Vue pour afficher les détails d'une location"""
    rental = get_object_or_404(Rental, id=rental_id)

    context = {
        'rental': rental,
    }

    return render(request, 'RentalApp/rental_detail.html', context)
