# Generated by Django 5.2.1 on 2025-06-30 17:57

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("TypeApp", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Equipement",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        max_length=100, verbose_name="Nom de l'équipement"
                    ),
                ),
                ("brand", models.CharField(max_length=50, verbose_name="Marque")),
                (
                    "quantity",
                    models.PositiveIntegerField(default=1, verbose_name="Quantité"),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[("available", "reserved")],
                        default="available",
                        max_length=20,
                        verbose_name="Statut",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="Date de création"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        auto_now=True, verbose_name="Date de modification"
                    ),
                ),
                (
                    "type",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="equipements",
                        to="TypeApp.type",
                        verbose_name="Type d'équipement",
                    ),
                ),
            ],
            options={
                "verbose_name": "Équipement",
                "verbose_name_plural": "Équipements",
                "ordering": ["name"],
            },
        ),
    ]
