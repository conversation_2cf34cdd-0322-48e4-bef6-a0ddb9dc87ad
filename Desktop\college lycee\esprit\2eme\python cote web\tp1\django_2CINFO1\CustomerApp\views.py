from django.shortcuts import render
from django.db.models import Q
from .models import Customer

# Create your views here.

def customer_list(request):
    """Vue pour afficher la liste des customers avec recherche"""
    customers = Customer.objects.all()
    search_query = request.GET.get('search', '')

    if search_query:
        customers = customers.filter(
            Q(customer_id__icontains=search_query) |
            Q(first_name__icontains=search_query) |
            Q(last_name__icontains=search_query)
        )

    context = {
        'customers': customers,
        'search_query': search_query,
    }

    return render(request, 'CustomerApp/customer_list.html', context)
