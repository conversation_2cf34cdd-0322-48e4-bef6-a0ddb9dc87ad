from django.shortcuts import render, redirect
from django.db.models import Q
from django.contrib import messages
from .models import Customer

# Create your views here.

def customer_list(request):
    """Vue pour afficher la liste des customers avec recherche"""
    customers = Customer.objects.all()
    search_query = request.GET.get('search', '')

    if search_query:
        customers = customers.filter(
            Q(customer_id__icontains=search_query) |
            Q(first_name__icontains=search_query) |
            Q(last_name__icontains=search_query)
        )

    context = {
        'customers': customers,
        'search_query': search_query,
    }

    return render(request, 'CustomerApp/customer_list.html', context)

def customer_login(request):
    """Vue pour la connexion des customers"""
    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')

        try:
            customer = Customer.objects.get(username=username)
            if customer.password == password:
                # Stocker l'ID du customer dans la session
                request.session['customer_id'] = customer.customer_id
                request.session['customer_name'] = customer.full_name
                messages.success(request, f'Connexion réussie ! Bienvenue {customer.full_name}')
                return redirect('CustomerApp:customer_login')
            else:
                messages.error(request, 'Mot de passe incorrect.')
        except Customer.DoesNotExist:
            messages.error(request, 'Nom d\'utilisateur introuvable.')

    return render(request, 'CustomerApp/login.html')

def customer_logout(request):
    """Vue pour la déconnexion du customer"""
    if 'customer_id' in request.session:
        customer_name = request.session.get('customer_name', 'Utilisateur')
        del request.session['customer_id']
        del request.session['customer_name']
        messages.success(request, f'{customer_name} a été déconnecté avec succès.')
    else:
        messages.info(request, 'Aucun utilisateur n\'était connecté.')

    return redirect('CustomerApp:customer_login')

def customer_dashboard(request):
    """Vue pour le tableau de bord du customer connecté"""
    customer_id = request.session.get('customer_id')
    if not customer_id:
        messages.error(request, 'Vous devez vous connecter pour accéder à cette page.')
        return redirect('CustomerApp:customer_login')

    try:
        customer = Customer.objects.get(customer_id=customer_id)
        context = {
            'customer': customer,
        }
        return render(request, 'CustomerApp/dashboard.html', context)
    except Customer.DoesNotExist:
        messages.error(request, 'Customer introuvable.')
        return redirect('CustomerApp:customer_login')

def customer_login(request):
    """Vue pour la connexion des customers"""
    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')

        try:
            customer = Customer.objects.get(username=username)
            if customer.password == password:
                # Stocker l'ID du customer dans la session
                request.session['customer_id'] = customer.customer_id
                request.session['customer_name'] = customer.full_name
                messages.success(request, f'Bienvenue {customer.full_name}!')
                return redirect('CustomerApp:customer_dashboard')
            else:
                messages.error(request, 'Mot de passe incorrect.')
        except Customer.DoesNotExist:
            messages.error(request, 'Nom d\'utilisateur introuvable.')

    return render(request, 'CustomerApp/login.html')

def customer_dashboard(request):
    """Vue pour le tableau de bord du customer connecté"""
    customer_id = request.session.get('customer_id')
    if not customer_id:
        messages.error(request, 'Vous devez vous connecter pour accéder à cette page.')
        return redirect('CustomerApp:customer_login')

    try:
        customer = Customer.objects.get(customer_id=customer_id)
        rentals = customer.rentals.select_related('equipement').order_by('-rental_date')

        context = {
            'customer': customer,
            'rentals': rentals,
        }

        return render(request, 'CustomerApp/dashboard.html', context)
    except Customer.DoesNotExist:
        messages.error(request, 'Customer introuvable.')
        return redirect('CustomerApp:customer_login')

def customer_logout(request):
    """Vue pour la déconnexion du customer"""
    if 'customer_id' in request.session:
        del request.session['customer_id']
    if 'customer_name' in request.session:
        del request.session['customer_name']
    messages.success(request, 'Vous avez été déconnecté avec succès.')
    return redirect('CustomerApp:customer_login')
