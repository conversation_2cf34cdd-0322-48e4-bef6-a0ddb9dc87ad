from django.db import models
from TypeApp.models import Type

# Create your models here.

class Equipement(models.Model):
    STATUS_CHOICES = [
        ('available', 'reserved'),
    ]

    name = models.CharField(max_length=100, verbose_name="Nom de l'équipement")
    brand = models.CharField(max_length=50, verbose_name="Marque")
    quantity = models.PositiveIntegerField(default=1, verbose_name="Quantité")
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='available',
        verbose_name="Statut"
    )
    type = models.ForeignKey(
        Type,
        on_delete=models.CASCADE,
        related_name='equipements',
        verbose_name="Type d'équipement"
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="Date de création")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="Date de modification")

    class Meta:
        verbose_name = "Équipement"
        verbose_name_plural = "Équipements"
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.brand})"

    @property
    def is_available(self):
        return self.status == 'available' and self.quantity > 0


       
        
