<h2>Update Conference</h2>

<div style="border: 1px solid #ccc; padding: 20px; margin: 20px 0; background-color: #f9f9f9;">
    <h3>Editing: {{ conference.title }}</h3>
    <p><strong>Current Category:</strong> {{ conference.category.title }}</p>
    <p><strong>Created:</strong> {{ conference.created_at }}</p>
    <p><strong>Last Updated:</strong> {{ conference.updated_at }}</p>
</div>

<form method="post" enctype="multipart/form-data">
    {% csrf_token %}
    
    <table border="1px" style="border-collapse: collapse; width: 100%;">
        <tr>
            <td style="padding: 10px; background-color: #f5f5f5; font-weight: bold;">Field</td>
            <td style="padding: 10px; background-color: #f5f5f5; font-weight: bold;">Value</td>
        </tr>
        {% for field in form %}
        <tr>
            <td style="padding: 10px; vertical-align: top;">
                <label for="{{ field.id_for_label }}">{{ field.label }}:</label>
                {% if field.help_text %}
                    <br><small style="color: #666;">{{ field.help_text }}</small>
                {% endif %}
            </td>
            <td style="padding: 10px;">
                {{ field }}
                {% if field.errors %}
                    <div style="color: red; font-size: 12px;">
                        {% for error in field.errors %}
                            <div>{{ error }}</div>
                        {% endfor %}
                    </div>
                {% endif %}
            </td>
        </tr>
        {% endfor %}
    </table>
    
    <div style="margin-top: 20px;">
        <button type="submit" style="background-color: #28a745; color: white; padding: 10px 20px; border: none; cursor: pointer;">
            Update Conference
        </button>
        <a href="{% url 'conference_detail_list' conference.id %}" style="background-color: #6c757d; color: white; padding: 10px 20px; text-decoration: none; margin-left: 10px;">
            Cancel
        </a>
    </div>
</form>

<div style="margin-top: 30px;">
    <a href="{% url 'conference_detail_list' conference.id %}">← Back to Conference Details</a> |
    <a href="{% url 'conference_class_list' %}">← Back to Conference List</a> |
    <a href="{% url 'conference_create' %}" style="color: green;">➕ Add New Conference</a>
</div>
