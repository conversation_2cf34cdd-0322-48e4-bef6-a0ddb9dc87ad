# Generated by Django 5.2.1 on 2025-06-30 17:57

from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Type",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.Char<PERSON>ield(max_length=100, verbose_name="Nom du type")),
                ("description", models.TextField(verbose_name="Description")),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="Date de création"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        auto_now=True, verbose_name="Date de modification"
                    ),
                ),
            ],
            options={
                "verbose_name": "Type d'équipement",
                "verbose_name_plural": "Types d'équipements",
                "ordering": ["name"],
            },
        ),
    ]
