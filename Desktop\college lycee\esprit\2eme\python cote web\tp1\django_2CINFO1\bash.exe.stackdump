Stack trace:
Frame         Function      Args
0007FFFFB740  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFB740, 0007FFFFA640) msys-2.0.dll+0x1FE8E
0007FFFFB740  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA18) msys-2.0.dll+0x67F9
0007FFFFB740  000210046832 (000210286019, 0007FFFFB5F8, 0007FFFFB740, 000000000000) msys-2.0.dll+0x6832
0007FFFFB740  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFB740  000210068E24 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBA20  00021006A225 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFFD5690000 ntdll.dll
7FFFB19C0000 aswhook.dll
7FFFD4C50000 KERNEL32.DLL
7FFFD2E80000 KERNELBASE.dll
7FFFD4AB0000 USER32.dll
7FFFD3690000 win32u.dll
7FFFD38B0000 GDI32.dll
7FFFD2D30000 gdi32full.dll
7FFFD35F0000 msvcp_win.dll
7FFFD3210000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFFD53D0000 advapi32.dll
7FFFD4E20000 msvcrt.dll
7FFFD4F20000 sechost.dll
7FFFD4980000 RPCRT4.dll
7FFFD2E50000 bcrypt.dll
7FFFD25E0000 CRYPTBASE.DLL
7FFFD3180000 bcryptPrimitives.dll
7FFFD4FC0000 IMM32.DLL
