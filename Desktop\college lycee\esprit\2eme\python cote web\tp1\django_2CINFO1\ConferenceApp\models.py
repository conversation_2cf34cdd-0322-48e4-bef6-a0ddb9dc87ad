from django.db import models
from django.utils import timezone
from django.core.validators import MinLengthValidator, MaxLengthValidator, RegexValidator
from UserApp.models import Participant
from django.core.exceptions import ValidationError

class Category(models.Model):
    title = models.CharField(max_length=200, unique=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.title
        
def validate_pdf(value):
    if not value.name.lower().endswith('.pdf'):
        raise ValidationError("Seuls les fichiers PDF sont autorisés.")

class Conference(models.Model):
    title = models.CharField(
        max_length=5,
        validators=[
            MinLengthValidator(5),
            MaxLengthValidator(100),
            RegexValidator(
                regex='^[a-zA-Z]{5}$',
                message='Le titre doit contenir exactement 5 lettres alphabétiques.'
            )
        ]
    )
    description = models.TextField(blank=True)
    start_date = models.DateTimeField(default=timezone.now)
    end_date = models.DateTimeField(default=timezone.now)
    location = models.CharField(max_length=200)
    price = models.FloatField()
    capacity = models.IntegerField()
    program = models.FileField(upload_to='files/', validators=[validate_pdf])
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    category = models.ForeignKey(Category, on_delete=models.CASCADE, related_name='conferences')

    def __str__(self):
        return self.title

class Reservation(models.Model):
    confirmed = models.BooleanField(default=False)
    reservation_date = models.DateTimeField(default=timezone.now)
    conference = models.ForeignKey(Conference, on_delete=models.CASCADE, related_name='reservations')
    participant = models.ForeignKey(Participant, on_delete=models.CASCADE)

    class Meta:
        unique_together = ('conference', 'participant')

    def __str__(self):
        return f"{self.participant} -> {self.conference}"
