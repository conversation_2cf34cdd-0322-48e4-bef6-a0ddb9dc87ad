from django.shortcuts import render
from .models import Conference
from django.views.generic import ListView,DetailView , CreateView,UpdateView,DeleteView
from django.urls import reverse_lazy

# Create your views here.
def conferenceList(request):
    list = Conference.objects.all().order_by("-price")
    return render(request,"conferences/conferenceList.html",{"conferences":list})

class ConferenceListView(ListView):
    model= Conference
    template_name = "conferences/conferenceList.html"
    context_object_name = "conferences"
    def get_queryset(self):
        return Conference.objects.all().order_by("-title")
    
class ConferenceDetailView(DetailView):
    model= Conference
    template_name = "conferences/conferenceDetaillList.html"
    context_object_name = "conference"

class ConferenceCreateView(CreateView):
    model= Conference
    template_name = "conferences/conferenceCreate.html"
    fields = ['title', 'description', 'start_date', 'end_date', 'location', 'price', 'capacity', 'program', 'category']
    success_url = reverse_lazy('conference_class_list')

class ConferenceUpdateView(UpdateView):
    model = Conference
    template_name = "conferences/conferenceUpdate.html"
    fields = ['title', 'description', 'start_date', 'end_date', 'location', 'price', 'capacity', 'program', 'category']
    context_object_name = "conference"
    success_url = reverse_lazy('conference_class_list')

class ConferenceDeleteView(DeleteView):
    model = Conference
    template_name = "conferences/conferenceDelete.html"
    context_object_name = "conference"
    success_url = reverse_lazy('conference_class_list')

