

{% load static %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"> 


<div class="container mt-5">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h2 class="text-primary">📅 Liste des conférences</h2>
        <a href="{% url 'conference_create' %}" class="btn btn-success">➕ Ajouter une conférence</a>
    </div>
    <body>
        <nav>
            {%if user.is_authenticated%}
              Welcome {{user.username}} = {{user.participant_category}}
              <a href = "{% url 'user_logout' %}">
                Logout
              </a>

            {% else %}
            <a href = "{% url 'user_register' %}">
                Register
            </a>

            <a href = "{% url 'user_login' %}">
                Login
            </a>

            {% endif %}
        </nav>
    </body>

    <div class="table-responsive">
        <table class="table table-bordered table-hover align-middle text-center">
            <thead class="table-dark">
                <tr>
                    <th>Titre</th>
                    <th>Date début</th>
                    <th>Date fin</th>
                    <th>Prix (DT)</th>
                    <th>Catégorie</th>
                    <th>Détail</th>
                    <th>Modifier</th>
                    <th>Supprimer</th>
                </tr>
            </thead>
            <tbody>
                {% for conference in conferences %}
                    <tr>
                        <td>{{ conference.title }}</td>
                        <td>{{ conference.start_date }}</td>
                        <td>{{ conference.end_date }}</td>
                        <td>{{ conference.price }}</td>
                        <td>{{ conference.category.title }}</td>
                        <td>
                            <a href="{% url 'conference_detail_list' conference.id %}" class="btn btn-info btn-sm">🔍 Voir</a>
                        </td>
                        <td>
                            <a href="{% url 'conference_update' conference.id %}" class="btn btn-warning btn-sm">✏ Modifier</a>
                        </td>
                        <td>
                            <form method="post" action="{% url 'conference_delete' conference.id %}" style="display:inline;">
                                {% csrf_token %}
                                <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('Confirmer la suppression ?');">🗑 Supprimer</button>
                            </form>
                        </td>
                        {% if user.is_authenticated %}
                         {%if conference.id in use_reservations %}
                         <p> Déjà reserver </p>
                         {% else %}
                        <td>
                             <form method="POST" action="{% url 'conference_reserve' conference.id %}" >
                                {% csrf_token %}
                                <button type="submit">📥 Réserver</button>
                                
                                </form>
                        </td>
                             {% endif %}
                             {% endif %}
                        
                    </tr>
                {% empty %}
                    <tr>
                        <td colspan="8" class="text-center text-muted">Aucune conférence à afficher</td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>