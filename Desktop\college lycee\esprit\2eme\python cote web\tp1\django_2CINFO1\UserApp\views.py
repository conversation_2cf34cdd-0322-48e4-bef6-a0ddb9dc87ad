from django.views.generic import CreateView
from django.contrib.auth.views import LoginView, LogoutView
from .models import Participant
from .forms import ParticipantCreationForm
from django.urls import reverse_lazy
from django.shortcuts import render

# Create your views here.
class UserCreateView(CreateView):
    model = Participant
    form_class = ParticipantCreationForm
    template_name = "users/usercreate.html"
    success_url = reverse_lazy('user_login')

class LoginCustomView(LoginView):
    template_name = "users/login.html"
    def get_success_url(self):
        return reverse_lazy('conference_class_list')

class LogoutCustomView(LogoutView):
    next_page = reverse_lazy('user_login')



# class LoginCustomView(LoginView):
#     template_name = "users/login.html"
     