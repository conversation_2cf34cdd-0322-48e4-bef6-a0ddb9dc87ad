<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Liste des Clients</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .search-form {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .search-form input[type="text"] {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 300px;
            margin-right: 10px;
        }
        .search-form button {
            padding: 8px 16px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .search-form button:hover {
            background-color: #0056b3;
        }
        .clear-search {
            margin-left: 10px;
            padding: 8px 16px;
            background-color: #6c757d;
            color: white;
            text-decoration: none;
            border-radius: 4px;
        }
        .clear-search:hover {
            background-color: #545b62;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #495057;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .no-results {
            text-align: center;
            padding: 40px;
            color: #6c757d;
            font-style: italic;
        }
        .results-count {
            margin-bottom: 10px;
            color: #6c757d;
            font-size: 14px;
        }
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-returned {
            background-color: #d4edda;
            color: #155724;
        }
        .status-active {
            background-color: #d1ecf1;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Liste des Clients</h1>
        
        <!-- Formulaire de recherche -->
        <div class="search-form">
            <form method="GET">
                <input type="text" 
                       name="search" 
                       value="{{ search_query }}" 
                       placeholder="Rechercher par Customer ID, Prénom ou Nom..."
                       autocomplete="off">
                <button type="submit">Rechercher</button>
                {% if search_query %}
                    <a href="{% url 'CustomerApp:customer_list' %}" class="clear-search">Effacer</a>
                {% endif %}
            </form>
        </div>

        <!-- Résultats -->
        {% if search_query %}
            <div class="results-count">
                <strong>{{ customers.count }}</strong> résultat(s) trouvé(s) pour "{{ search_query }}"
            </div>
        {% else %}
            <div class="results-count">
                <strong>{{ customers.count }}</strong> client(s) au total
            </div>
        {% endif %}

        <!-- Tableau des clients -->
        {% if customers %}
            <table>
                <thead>
                    <tr>
                        <th>Customer ID</th>
                        <th>Prénom</th>
                        <th>Nom de famille</th>
                        <th>Email</th>
                        <th>Username</th>
                        <th>Statut</th>
                    </tr>
                </thead>
                <tbody>
                    {% for customer in customers %}
                        <tr>
                            <td><strong>{{ customer.customer_id }}</strong></td>
                            <td>{{ customer.first_name }}</td>
                            <td>{{ customer.last_name }}</td>
                            <td>{{ customer.email }}</td>
                            <td>{{ customer.username }}</td>
                            <td>
                                {% if customer.returned %}
                                    <span class="status-badge status-returned">Retourné</span>
                                {% else %}
                                    <span class="status-badge status-active">Actif</span>
                                {% endif %}
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        {% else %}
            <div class="no-results">
                {% if search_query %}
                    Aucun client trouvé pour "{{ search_query }}"
                {% else %}
                    Aucun client enregistré
                {% endif %}
            </div>
        {% endif %}
    </div>
</body>
</html>
