from django.urls import path
from .views import *

urlpatterns = [
    path('list/', conferenceList,name = "conference_list"),
    path('list_class/', ConferenceListView.as_view(), name ="conference_class_list"),
    path('list_detail/<int:pk>/', ConferenceDetailView.as_view(), name ="conference_detail_list"),
    path('create/', ConferenceCreateView.as_view(), name ="conference_create"),
    path('update/<int:pk>/', ConferenceUpdateView.as_view(), name ="conference_update"),
    path('delete/<int:pk>/', ConferenceDeleteView.as_view(), name ="conference_delete")
]