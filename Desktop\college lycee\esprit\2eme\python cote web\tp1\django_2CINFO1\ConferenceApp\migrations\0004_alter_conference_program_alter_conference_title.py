# Generated by Django 4.2 on 2025-06-03 18:04

import ConferenceApp.models
import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("ConferenceApp", "0003_alter_conference_title"),
    ]

    operations = [
        migrations.AlterField(
            model_name="conference",
            name="program",
            field=models.FileField(
                upload_to="files/", validators=[ConferenceApp.models.validate_pdf]
            ),
        ),
        migrations.AlterField(
            model_name="conference",
            name="title",
            field=models.CharField(
                max_length=5,
                validators=[
                    django.core.validators.MinLengthValidator(5),
                    django.core.validators.MaxLengthValidator(100),
                    django.core.validators.RegexValidator(
                        message="Le titre doit contenir exactement 5 lettres alphabétiques.",
                        regex="^[a-zA-Z]{5}$",
                    ),
                ],
            ),
        ),
    ]
