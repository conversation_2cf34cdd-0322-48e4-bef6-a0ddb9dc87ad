<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Liste des Équipements</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .container {
            display: flex;
            min-height: 100vh;
        }
        .sidebar {
            width: 300px;
            background-color: white;
            padding: 20px;
            box-shadow: 2px 0 4px rgba(0,0,0,0.1);
            position: fixed;
            height: 100vh;
            overflow-y: auto;
        }
        .main-content {
            flex: 1;
            margin-left: 300px;
            padding: 20px;
        }
        .content-wrapper {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            margin-bottom: 30px;
        }
        .sidebar h3 {
            color: #333;
            margin-bottom: 15px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }
        .filter-group {
            margin-bottom: 25px;
        }
        .filter-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        .filter-group input, .filter-group select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .filter-buttons {
            margin-top: 20px;
        }
        .btn {
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .btn:hover {
            opacity: 0.8;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #495057;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-available {
            background-color: #d4edda;
            color: #155724;
        }
        .status-rented {
            background-color: #fff3cd;
            color: #856404;
        }
        .status-maintenance {
            background-color: #f8d7da;
            color: #721c24;
        }
        .status-out_of_order {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        .pagination {
            margin-top: 20px;
            text-align: center;
        }
        .pagination a, .pagination span {
            display: inline-block;
            padding: 8px 12px;
            margin: 0 4px;
            text-decoration: none;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .pagination a:hover {
            background-color: #f5f5f5;
        }
        .pagination .current {
            background-color: #007bff;
            color: white;
            border-color: #007bff;
        }
        .results-info {
            margin-bottom: 15px;
            color: #6c757d;
            font-size: 14px;
        }
        .no-results {
            text-align: center;
            padding: 40px;
            color: #6c757d;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Barre latérale avec filtres -->
        <div class="sidebar">
            <h3>🔍 Filtres de recherche</h3>
            
            <form method="GET" id="filterForm">
                <div class="filter-group">
                    <label for="search">Recherche générale:</label>
                    <input type="text" 
                           id="search"
                           name="search" 
                           value="{{ search_query }}" 
                           placeholder="Nom, marque, type, statut...">
                </div>

                <div class="filter-group">
                    <label for="brand_search">Recherche par marque:</label>
                    <input type="text" 
                           id="brand_search"
                           name="brand_search" 
                           value="{{ brand_search }}" 
                           placeholder="Entrez une marque...">
                </div>

                <div class="filter-group">
                    <label for="brand_filter">Filtrer par marque:</label>
                    <select name="brand_filter" id="brand_filter">
                        <option value="">Toutes les marques</option>
                        {% for brand in all_brands %}
                            <option value="{{ brand }}" {% if brand_filter == brand %}selected{% endif %}>
                                {{ brand }}
                            </option>
                        {% endfor %}
                    </select>
                </div>

                <div class="filter-group">
                    <label for="status_filter">Filtrer par statut:</label>
                    <select name="status_filter" id="status_filter">
                        <option value="">Tous les statuts</option>
                        <option value="available" {% if status_filter == 'available' %}selected{% endif %}>Disponible</option>
                        <option value="rented" {% if status_filter == 'rented' %}selected{% endif %}>Loué</option>
                        <option value="maintenance" {% if status_filter == 'maintenance' %}selected{% endif %}>En maintenance</option>
                        <option value="out_of_order" {% if status_filter == 'out_of_order' %}selected{% endif %}>Hors service</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label for="type_filter">Filtrer par type:</label>
                    <select name="type_filter" id="type_filter">
                        <option value="">Tous les types</option>
                        {% for type in all_types %}
                            <option value="{{ type.id }}" {% if type_filter == type.id|stringformat:"s" %}selected{% endif %}>
                                {{ type.name }}
                            </option>
                        {% endfor %}
                    </select>
                </div>

                <div class="filter-buttons">
                    <button type="submit" class="btn btn-primary">Appliquer les filtres</button>
                    <a href="{% url 'EquipementApp:equipement_list' %}" class="btn btn-secondary">Réinitialiser</a>
                </div>
            </form>
        </div>

        <!-- Contenu principal -->
        <div class="main-content">
            <div class="content-wrapper">
                <h1>📦 Liste des Équipements</h1>
                
                <!-- Informations sur les résultats -->
                <div class="results-info">
                    {% if page_obj %}
                        Affichage de {{ page_obj.start_index }} à {{ page_obj.end_index }} 
                        sur {{ paginator.count }} équipement(s)
                        {% if search_query or brand_search or brand_filter or status_filter or type_filter %}
                            (filtré)
                        {% endif %}
                    {% endif %}
                </div>

                <!-- Tableau des équipements -->
                {% if page_obj %}
                    <table>
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Nom</th>
                                <th>Marque</th>
                                <th>Type</th>
                                <th>Quantité</th>
                                <th>Statut</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for equipement in page_obj %}
                                <tr>
                                    <td><strong>{{ equipement.id }}</strong></td>
                                    <td>{{ equipement.name }}</td>
                                    <td>{{ equipement.brand }}</td>
                                    <td>{{ equipement.type.name }}</td>
                                    <td>{{ equipement.quantity }}</td>
                                    <td>
                                        <span class="status-badge status-{{ equipement.status }}">
                                            {{ equipement.get_status_display }}
                                        </span>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>

                    <!-- Pagination -->
                    {% if page_obj.has_other_pages %}
                        <div class="pagination">
                            {% if page_obj.has_previous %}
                                <a href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page=1">&laquo; Premier</a>
                                <a href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.previous_page_number }}">Précédent</a>
                            {% endif %}

                            <span class="current">
                                Page {{ page_obj.number }} sur {{ page_obj.paginator.num_pages }}
                            </span>

                            {% if page_obj.has_next %}
                                <a href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.next_page_number }}">Suivant</a>
                                <a href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.paginator.num_pages }}">Dernier &raquo;</a>
                            {% endif %}
                        </div>
                    {% endif %}
                {% else %}
                    <div class="no-results">
                        Aucun équipement trouvé avec les critères sélectionnés.
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</body>
</html>
