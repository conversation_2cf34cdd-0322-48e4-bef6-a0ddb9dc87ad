from django.db import models
from django.contrib.auth.models import AbstractUser

# Create your models here.
class Participant(AbstractUser):
    cin = models.Char<PERSON><PERSON>(primary_key=True, max_length=8)
    email = models.EmailField(max_length=250, unique=True)
    first_name= models.Char<PERSON><PERSON>(max_length=200)
    last_name= models.Char<PERSON><PERSON>(max_length=200)
    username = models.Char<PERSON><PERSON>(unique=True, max_length=150)
    
    CHOICE = (
        ('ETUDIANT', 'Etudiant'),
        ('CHERCHEUR', 'CHERCHEUR'),
        ('ENSEIGNANT', 'ENSEIGNANT'),
        ('DOCTEUR', 'DOCTEUR'),
    )
    participant_category = models.Char<PERSON><PERSON>('category', choices=CHOICE, max_length=100)
    
    USERNAME_Field = 'username'
    
    