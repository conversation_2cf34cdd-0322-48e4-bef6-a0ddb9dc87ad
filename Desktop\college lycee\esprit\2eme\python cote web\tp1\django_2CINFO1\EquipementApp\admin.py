from django.contrib import admin
from .models import Equipement

# Register your models here.

@admin.register(Equipement)
class EquipementAdmin(admin.ModelAdmin):
    list_display = ['name', 'brand', 'type', 'quantity', 'status', 'created_at']
    list_filter = ['type', 'status', 'brand', 'created_at']
    search_fields = ['name', 'brand', 'type__name']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('Informations générales', {
            'fields': ('name', 'brand', 'type')
        }),
        ('Disponibilité', {
            'fields': ('quantity', 'status')
        }),
        ('Dates', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('type')
        
