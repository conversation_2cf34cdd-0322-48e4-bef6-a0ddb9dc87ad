from django.shortcuts import render
from django.db.models import Q
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
from .models import Equipement
from TypeApp.models import Type

# Create your views here.

def equipement_list(request):
    """Vue pour afficher la liste des équipements avec filtres dans la barre latérale et pagination"""
    equipements = Equipement.objects.select_related('type').all()

    # Récupération des paramètres de filtrage
    search_query = request.GET.get('search', '')
    brand_search = request.GET.get('brand_search', '')
    brand_filter = request.GET.get('brand_filter', '')
    status_filter = request.GET.get('status_filter', '')
    type_filter = request.GET.get('type_filter', '')

    # Application des filtres
    if search_query:
        equipements = equipements.filter(
            Q(name__icontains=search_query) |
            Q(brand__icontains=search_query) |
            Q(type__name__icontains=search_query) |
            Q(status__icontains=search_query)
        )

    if brand_search:
        equipements = equipements.filter(brand__icontains=brand_search)

    if brand_filter:
        equipements = equipements.filter(brand=brand_filter)

    if status_filter:
        equipements = equipements.filter(status=status_filter)

    if type_filter:
        equipements = equipements.filter(type_id=type_filter)

    # Pagination - 3 équipements par page
    paginator = Paginator(equipements, 3)
    page_number = request.GET.get('page')

    try:
        page_obj = paginator.get_page(page_number)
    except PageNotAnInteger:
        # Si le numéro de page n'est pas un entier, afficher la première page
        page_obj = paginator.get_page(1)
    except EmptyPage:
        # Si la page est hors de portée, afficher la dernière page
        page_obj = paginator.get_page(paginator.num_pages)

    # Données pour les filtres
    all_brands = Equipement.objects.values_list('brand', flat=True).distinct().order_by('brand')
    all_types = Type.objects.all().order_by('name')

    context = {
        'equipements': page_obj,
        'search_query': search_query,
        'brand_search': brand_search,
        'brand_filter': brand_filter,
        'status_filter': status_filter,
        'type_filter': type_filter,
        'all_brands': all_brands,
        'all_types': all_types,
        'paginator': paginator,
        'page_obj': page_obj,
    }

    return render(request, 'EquipementApp/equipement_list.html', context)
