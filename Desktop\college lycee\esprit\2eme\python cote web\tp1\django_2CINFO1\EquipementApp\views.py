from django.shortcuts import render
from django.db.models import Q
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
from .models import Equipement

# Create your views here.

def equipement_list(request):
    """Vue pour afficher la liste des équipements avec pagination"""
    equipements = Equipement.objects.select_related('type').all()
    search_query = request.GET.get('search', '')

    if search_query:
        equipements = equipements.filter(
            Q(name__icontains=search_query) |
            Q(brand__icontains=search_query) |
            Q(type__name__icontains=search_query) |
            Q(status__icontains=search_query)
        )

    # Pagination - 3 équipements par page
    paginator = Paginator(equipements, 3)
    page_number = request.GET.get('page')

    try:
        page_obj = paginator.get_page(page_number)
    except PageNotAnInteger:
        # Si le numéro de page n'est pas un entier, afficher la première page
        page_obj = paginator.get_page(1)
    except EmptyPage:
        # Si la page est hors de portée, afficher la dernière page
        page_obj = paginator.get_page(paginator.num_pages)

    context = {
        'equipements': page_obj,
        'search_query': search_query,
        'paginator': paginator,
        'page_obj': page_obj,
    }

    return render(request, 'EquipementApp/equipement_list.html', context)
