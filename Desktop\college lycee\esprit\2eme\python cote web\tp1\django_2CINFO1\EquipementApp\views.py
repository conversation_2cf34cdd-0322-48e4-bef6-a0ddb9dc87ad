from django.shortcuts import render
from django.db.models import Q
from .models import Equipement

# Create your views here.

def equipement_list(request):
    """Vue pour afficher la liste des équipements"""
    equipements = Equipement.objects.select_related('type').all()
    search_query = request.GET.get('search', '')

    if search_query:
        equipements = equipements.filter(
            Q(name__icontains=search_query) |
            Q(brand__icontains=search_query) |
            Q(type__name__icontains=search_query) |
            Q(status__icontains=search_query)
        )

    context = {
        'equipements': equipements,
        'search_query': search_query,
    }

    return render(request, 'EquipementApp/equipement_list.html', context)
