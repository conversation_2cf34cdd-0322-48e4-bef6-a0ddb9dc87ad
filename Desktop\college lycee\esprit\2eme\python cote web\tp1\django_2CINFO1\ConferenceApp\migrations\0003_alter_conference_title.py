# Generated by Django 5.2.1 on 2025-06-02 18:14

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("ConferenceApp", "0002_reservation"),
    ]

    operations = [
        migrations.AlterField(
            model_name="conference",
            name="title",
            field=models.CharField(
                max_length=5,
                validators=[
                    django.core.validators.MinLengthValidator(5),
                    django.core.validators.MaxLengthValidator(5),
                    django.core.validators.RegexValidator(
                        message="Le titre doit contenir exactement 5 lettres alphabétiques.",
                        regex="^[a-zA-Z]{5}$",
                    ),
                ],
            ),
        ),
    ]
